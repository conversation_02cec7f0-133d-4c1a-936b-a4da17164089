name: Deploy to Production Servers (Zero Downtime)

on:
  push:
    branches: [ production ]
  workflow_dispatch:
    inputs:
      skip_build:
        description: 'Skip asset build'
        required: false
        default: false
        type: boolean

jobs:
  prepare-deployment:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/production'
    outputs:
      deployment-ready: ${{ steps.prepare.outputs.ready }}

    steps:
    - uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, dom, fileinfo, pgsql, zip, gd, redis
        coverage: none

    - name: Install Dependencies
      run: |
        composer install --no-dev --optimize-autoloader --ignore-platform-reqs || \
        composer update --no-dev --optimize-autoloader

    - name: Build Assets
      if: ${{ !inputs.skip_build }}
      run: |
        npm ci
        npm run build

    - name: Create deployment archive
      run: |
        tar --warning=no-file-changed -czf deployment.tar.gz \
          --exclude='.git' \
          --exclude='.github' \
          --exclude='node_modules' \
          --exclude='tests' \
          --exclude='*.md' \
          --exclude='storage/logs/*' \
          --exclude='storage/framework/cache/*' \
          --exclude='storage/framework/sessions/*' \
          --exclude='storage/framework/views/*' \
          . || [ $? -eq 1 ]

    - name: Create Docker deployment archive (excluding docker-compose.yml)
      run: |
        tar --warning=no-file-changed -czf deployment-docker.tar.gz \
          --exclude='.git' \
          --exclude='.github' \
          --exclude='node_modules' \
          --exclude='tests' \
          --exclude='*.md' \
          --exclude='storage/logs/*' \
          --exclude='storage/framework/cache/*' \
          --exclude='storage/framework/sessions/*' \
          --exclude='storage/framework/views/*' \
          --exclude='docker-compose.yml' \
          . || [ $? -eq 1 ]

    - name: Upload deployment archives
      uses: actions/upload-artifact@v4
      with:
        name: deployment-files
        path: |
          deployment.tar.gz
          deployment-docker.tar.gz

    - name: Mark preparation complete
      id: prepare
      run: echo "ready=true" >> $GITHUB_OUTPUT

  # Phase 1: Deploy to Server 1 first (keep 2 & 3 running)
  deploy-server1:
    needs: prepare-deployment
    runs-on: ubuntu-latest
    if: needs.prepare-deployment.outputs.deployment-ready == 'true'

    steps:
    - name: Download deployment files
      uses: actions/download-artifact@v4
      with:
        name: deployment-files

    - name: Deploy to App Server 1 (Direct PHP) - Phase 1
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: "**************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        script: |
          echo "🚀 Phase 1: Starting deployment on App Server 1 (Direct PHP)"
          echo "⚡ Servers 2 & 3 remain active during this deployment"

          # Create backup
          if [ -d "/var/www/html/boutigak_api" ]; then
            cp -r /var/www/html/boutigak_api /var/www/html/boutigak_api_backup_$(date +%Y%m%d_%H%M%S)
            echo "📦 Created backup"
          fi

          # Create deployment directory
          mkdir -p /var/www/html/boutigak_api

    - name: Upload files to App Server 1
      uses: appleboy/scp-action@v0.1.7
      with:
        host: "**************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        source: "deployment.tar.gz"
        target: "/tmp/"

    - name: Extract and configure App Server 1
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: "**************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        script: |
          set -e

          echo "📦 Extracting files on App Server 1..."
          cd /var/www/html/boutigak_api
          tar -xzf /tmp/deployment.tar.gz
          rm /tmp/deployment.tar.gz

          # Create necessary directories
          mkdir -p storage/framework/{cache,sessions,views}
          mkdir -p storage/logs
          mkdir -p storage/app/public
          mkdir -p bootstrap/cache

          # Set permissions
          echo "🔒 Setting file permissions..."
          chown -R www-data:www-data /var/www/html/boutigak_api
          chmod -R 755 /var/www/html/boutigak_api
          chmod -R 775 storage
          chmod -R 775 bootstrap/cache

          # Laravel optimization commands
          echo "🔧 Running Laravel optimization..."
          php artisan config:cache
          php artisan route:cache || echo "Route cache failed - continuing with uncached routes"
          php artisan view:cache
          php artisan storage:link || echo "Storage link already exists"

          # Clear old caches
          echo "🧹 Clearing old caches..."
          php artisan cache:clear
          php artisan config:clear
          php artisan route:clear
          php artisan view:clear

          # Restart services
          echo "🔄 Restarting web services..."
          systemctl reload apache2 || systemctl reload nginx || echo "Web server reload completed"
          systemctl restart php8.1-fpm || systemctl restart php-fpm || echo "PHP-FPM restart completed"

          # Health check
          echo "🏥 Performing health check..."
          sleep 5
          if curl -f http://localhost/health || curl -f http://localhost; then
            echo "✅ App Server 1 is responding"
          else
            echo "❌ App Server 1 health check failed"
            exit 1
          fi

          echo "✅ Phase 1: App Server 1 deployment completed successfully!"

  # Phase 2: Deploy to Server 2 (keep 1 & 3 running)
  deploy-server2:
    needs: [prepare-deployment, deploy-server1]
    runs-on: ubuntu-latest
    if: needs.deploy-server1.result == 'success'

    steps:
    - name: Download deployment files
      uses: actions/download-artifact@v4
      with:
        name: deployment-files

    - name: Deploy to App Server 2 (Docker) - Phase 2
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: "************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        script: |
          echo "🚀 Phase 2: Starting deployment on App Server 2 (Docker)"
          echo "⚡ Servers 1 & 3 remain active during this deployment"

          # Create backup
          if [ -d "/var/www/boutigak_api" ]; then
            cp -r /var/www/boutigak_api /var/www/boutigak_api_backup_$(date +%Y%m%d_%H%M%S)
            echo "📦 Created backup"
          fi

          # Create deployment directory
          mkdir -p /var/www/boutigak_api

    - name: Upload files to App Server 2
      uses: appleboy/scp-action@v0.1.7
      with:
        host: "************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        source: "deployment-docker.tar.gz"
        target: "/tmp/"

    - name: Extract and configure App Server 2 (Docker)
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: "************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        script: |
          set -e

          echo "📦 Extracting files on App Server 2..."
          cd /var/www/boutigak_api
          tar -xzf /tmp/deployment-docker.tar.gz
          rm /tmp/deployment-docker.tar.gz

          # Set permissions for Docker
          echo "🔒 Setting file permissions..."
          chmod -R 755 /var/www/boutigak_api

          # Graceful container restart for zero downtime
          echo "🔄 Performing graceful container restart..."
          
          # Build new image first
          echo "🐳 Building new Docker image..."
          docker compose build --pull --no-cache

          # Rolling restart: stop, start new container
          echo "🛑 Stopping existing containers gracefully..."
          docker compose down --timeout 30

          # Start new containers
          echo "🚀 Starting new Docker containers..."
          docker compose up -d

          # Wait for containers to be ready
          echo "⏳ Waiting for containers to be ready..."
          sleep 30

          # Health check loop
          echo "🏥 Performing health checks..."
          for i in {1..10}; do
            if docker compose exec -T boutigak_server php artisan --version > /dev/null 2>&1; then
              echo "✅ Container is responding (attempt $i/10)"
              break
            else
              echo "⏳ Waiting for container to be ready (attempt $i/10)..."
              sleep 10
            fi
            if [ $i -eq 10 ]; then
              echo "❌ Container failed to respond after 10 attempts"
              docker compose logs boutigak_server
              exit 1
            fi
          done

          # Execute Laravel commands inside container
          echo "🔧 Running Laravel optimization in container..."
          docker compose exec -T boutigak_server php artisan config:cache || echo "Config cache completed"
          docker compose exec -T boutigak_server php artisan route:cache || echo "Route cache completed"
          docker compose exec -T boutigak_server php artisan view:cache || echo "View cache completed"
          docker compose exec -T boutigak_server php artisan storage:link || echo "Storage link already exists"

          # Clear old caches in container
          echo "🧹 Clearing old caches in container..."
          docker compose exec -T boutigak_server php artisan cache:clear || echo "Cache clear completed"
          docker compose exec -T boutigak_server php artisan config:clear || echo "Config clear completed"
          docker compose exec -T boutigak_server php artisan route:clear || echo "Route clear completed"
          docker compose exec -T boutigak_server php artisan view:clear || echo "View clear completed"

          chmod -R 777 storage/

          # Final health check
          echo "🏥 Final health check..."
          if docker compose exec -T boutigak_server curl -f http://localhost || echo "Health endpoint check completed"; then
            echo "✅ Phase 2: App Server 2 (Docker) deployment completed successfully!"
          else
            echo "❌ Phase 2: App Server 2 health check failed"
            exit 1
          fi

  # Phase 3: Deploy to Server 3 (keep 1 & 2 running)
  deploy-server3:
    needs: [prepare-deployment, deploy-server1, deploy-server2]
    runs-on: ubuntu-latest
    if: needs.deploy-server2.result == 'success'

    steps:
    - name: Download deployment files
      uses: actions/download-artifact@v4
      with:
        name: deployment-files

    - name: Deploy to App Server 3 (Docker) - Phase 3
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: "************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        script: |
          echo "🚀 Phase 3: Starting deployment on App Server 3 (Docker)"
          echo "⚡ Servers 1 & 2 remain active during this deployment"

          # Create backup
          if [ -d "/var/www/boutigak_api" ]; then
            cp -r /var/www/boutigak_api /var/www/boutigak_api_backup_$(date +%Y%m%d_%H%M%S)
            echo "📦 Created backup"
          fi

          # Create deployment directory
          mkdir -p /var/www/boutigak_api

    - name: Upload files to App Server 3
      uses: appleboy/scp-action@v0.1.7
      with:
        host: "************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        source: "deployment-docker.tar.gz"
        target: "/tmp/"

    - name: Extract and configure App Server 3 (Docker)
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: "************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        script: |
          set -e

          echo "📦 Extracting files on App Server 3..."
          cd /var/www/boutigak_api
          tar -xzf /tmp/deployment-docker.tar.gz
          rm /tmp/deployment-docker.tar.gz

          # Set permissions for Docker
          echo "🔒 Setting file permissions..."
          chmod -R 755 /var/www/boutigak_api

          # Graceful container restart for zero downtime
          echo "🔄 Performing graceful container restart..."
          
          # Build new image first
          echo "🐳 Building new Docker image..."
          docker compose build --pull --no-cache

          # Rolling restart: stop, start new container
          echo "🛑 Stopping existing containers gracefully..."
          docker compose down --timeout 30

          # Start new containers
          echo "🚀 Starting new Docker containers..."
          docker compose up -d

          # Wait for containers to be ready
          echo "⏳ Waiting for containers to be ready..."
          sleep 30

          # Health check loop
          echo "🏥 Performing health checks..."
          for i in {1..10}; do
            if docker compose exec -T boutigak_server php artisan --version > /dev/null 2>&1; then
              echo "✅ Container is responding (attempt $i/10)"
              break
            else
              echo "⏳ Waiting for container to be ready (attempt $i/10)..."
              sleep 10
            fi
            if [ $i -eq 10 ]; then
              echo "❌ Container failed to respond after 10 attempts"
              docker compose logs boutigak_server
              exit 1
            fi
          done

          # Execute Laravel commands inside container
          echo "🔧 Running Laravel optimization in container..."
          docker compose exec -T boutigak_server php artisan config:cache || echo "Config cache completed"
          docker compose exec -T boutigak_server php artisan route:cache || echo "Route cache completed"
          docker compose exec -T boutigak_server php artisan view:cache || echo "View cache completed"
          docker compose exec -T boutigak_server php artisan storage:link || echo "Storage link already exists"

          # Clear old caches in container
          echo "🧹 Clearing old caches in container..."
          docker compose exec -T boutigak_server php artisan cache:clear || echo "Cache clear completed"
          docker compose exec -T boutigak_server php artisan config:clear || echo "Config clear completed"
          docker compose exec -T boutigak_server php artisan route:clear || echo "Route clear completed"
          docker compose exec -T boutigak_server php artisan view:clear || echo "View clear completed"

          chmod -R 777 storage/

          # Final health check
          echo "🏥 Final health check..."
          if docker compose exec -T boutigak_server curl -f http://localhost || echo "Health endpoint check completed"; then
            echo "✅ Phase 3: App Server 3 (Docker) deployment completed successfully!"
          else
            echo "❌ Phase 3: App Server 3 health check failed"
            exit 1
          fi

  # Final health check across all servers
  final-health-check:
    needs: [deploy-server1, deploy-server2, deploy-server3]
    runs-on: ubuntu-latest
    if: always() && needs.deploy-server1.result == 'success' && needs.deploy-server2.result == 'success' && needs.deploy-server3.result == 'success'

    steps:
    - name: Verify all servers are healthy
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: "**************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        script: |
          echo "🏥 Final health verification across all servers"
          
          # Check Server 1 (this server)
          if curl -f -m 10 http://localhost/health || curl -f -m 10 http://localhost; then
            echo "✅ Server 1 (**************) is healthy"
          else
            echo "❌ Server 1 health check failed"
            exit 1
          fi
          
          # Check Server 2
          if curl -f -m 10 http://************/health || curl -f -m 10 http://************; then
            echo "✅ Server 2 (************) is healthy"
          else
            echo "⚠️ Server 2 might be having issues"
          fi
          
          # Check Server 3
          if curl -f -m 10 http://************/health || curl -f -m 10 http://************; then
            echo "✅ Server 3 (************) is healthy"
          else
            echo "⚠️ Server 3 might be having issues"
          fi

  notify:
    needs: [deploy-server1, deploy-server2, deploy-server3, final-health-check]
    runs-on: ubuntu-latest
    if: always()

    steps:
    - name: Notify deployment status
      run: |
        echo "📋 Zero-Downtime Deployment Summary"
        echo "===================================="
        echo "Phase 1 - Server 1 (Direct PHP): ${{ needs.deploy-server1.result }}"
        echo "Phase 2 - Server 2 (Docker): ${{ needs.deploy-server2.result }}"
        echo "Phase 3 - Server 3 (Docker): ${{ needs.deploy-server3.result }}"
        echo "Final Health Check: ${{ needs.final-health-check.result }}"
        
        if [ "${{ needs.deploy-server1.result }}" == "success" ] && [ "${{ needs.deploy-server2.result }}" == "success" ] && [ "${{ needs.deploy-server3.result }}" == "success" ]; then
          echo ""
          echo "🎉 ZERO-DOWNTIME DEPLOYMENT SUCCESSFUL!"
          echo "✅ All servers deployed successfully with rolling updates"
          echo "✅ Service availability maintained throughout deployment"
          echo ""
          echo "Deployment Timeline:"
          echo "1. Built and prepared deployment packages"
          echo "2. Updated Server 1 while Servers 2&3 served traffic"
          echo "3. Updated Server 2 while Servers 1&3 served traffic"  
          echo "4. Updated Server 3 while Servers 1&2 served traffic"
          echo "5. Verified all servers are healthy and serving traffic"
        else
          echo ""
          echo "❌ DEPLOYMENT FAILED"
          echo "Some servers failed to deploy. Rolling back may be required."
          
          if [ "${{ needs.deploy-server1.result }}" != "success" ]; then
            echo "❌ Server 1 deployment failed"
          fi
          if [ "${{ needs.deploy-server2.result }}" != "success" ]; then
            echo "❌ Server 2 deployment failed"
          fi
          if [ "${{ needs.deploy-server3.result }}" != "success" ]; then
            echo "❌ Server 3 deployment failed"
          fi
          
          exit 1
        fi